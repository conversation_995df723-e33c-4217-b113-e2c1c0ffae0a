{% extends 'base.html' %}
{% load static %}

{% block title %}جميع التدخلات - الجداول المتقدمة{% endblock %}

{% block extra_css %}
<style>
    .all-interventions-container {
        padding: 20px;
        background: #f8f9fa;
        min-height: 100vh;
    }
    
    .page-header {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3);
    }
    
    .page-title {
        font-size: 2.5rem;
        font-weight: 300;
        margin: 0;
        text-align: center;
    }
    
    .page-subtitle {
        text-align: center;
        margin-top: 10px;
        opacity: 0.9;
        font-size: 1.1rem;
    }
    
    .intervention-types-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
        margin-bottom: 30px;
    }
    
    .intervention-type-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border: none;
        text-decoration: none;
        color: inherit;
        cursor: pointer;
        text-align: center;
    }
    
    .intervention-type-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        text-decoration: none;
        color: inherit;
    }
    
    .card-icon {
        font-size: 3rem;
        margin-bottom: 15px;
        display: block;
    }
    
    .card-medical .card-icon { color: #dc3545; }
    .card-accident .card-icon { color: #fd7e14; }
    .card-agricultural .card-icon { color: #28a745; }
    .card-building .card-icon { color: #e83e8c; }
    .card-misc .card-icon { color: #6f42c1; }
    
    .card-title {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .card-description {
        color: #6c757d;
        font-size: 0.95rem;
        line-height: 1.5;
        margin-bottom: 15px;
    }
    
    .card-stats {
        display: flex;
        justify-content: space-around;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #e9ecef;
    }
    
    .stat-item {
        text-align: center;
    }
    
    .stat-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #495057;
    }
    
    .stat-label {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 2px;
    }
    
    .back-button {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 12px 30px;
        border: none;
        border-radius: 25px;
        font-size: 1.1rem;
        text-decoration: none;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        display: inline-block;
        margin-bottom: 20px;
    }
    
    .back-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .development-notice {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .development-notice h4 {
        color: #856404;
        margin-bottom: 10px;
    }
    
    .development-notice p {
        color: #856404;
        margin: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="all-interventions-container">
    <!-- Back Button -->
    <a href="{% url 'daily_interventions' %}" class="back-button">
        <i class="fas fa-arrow-right"></i> العودة إلى التدخلات اليومية
    </a>

    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">🗂️ جميع التدخلات - الجداول المتقدمة</h1>
        <p class="page-subtitle">عرض وإدارة جداول التدخلات المتخصصة لكل نوع تدخل</p>
    </div>

    <!-- Development Notice -->
    <div class="development-notice">
        <h4><i class="fas fa-tools"></i> تحت التطوير</h4>
        <p>هذه الصفحة قيد التطوير. سيتم إضافة الجداول المتخصصة لكل نوع تدخل قريباً.</p>
    </div>

    <!-- Intervention Types Grid -->
    <div class="intervention-types-grid">
        <!-- Medical Evacuation -->
        <div class="intervention-type-card card-medical" onclick="openInterventionTable('medical')">
            <i class="fas fa-ambulance card-icon"></i>
            <div class="card-title">📋 جدول الإجلاء الصحي</div>
            <div class="card-description">
                عرض تفاصيل جميع عمليات الإجلاء الصحي مع البيانات الكاملة من البلاغ الأولي حتى إنهاء المهمة
            </div>
            <div class="card-stats">
                <div class="stat-item">
                    <div class="stat-number">28</div>
                    <div class="stat-label">حقل بيانات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">مراحل</div>
                </div>
            </div>
        </div>

        <!-- Traffic Accidents -->
        <div class="intervention-type-card card-accident" onclick="openInterventionTable('accident')">
            <i class="fas fa-car-crash card-icon"></i>
            <div class="card-title">📋 جدول حوادث المرور</div>
            <div class="card-description">
                عرض تفاصيل جميع حوادث المرور مع معلومات الضحايا والخسائر المادية ونوع الطريق
            </div>
            <div class="card-stats">
                <div class="stat-item">
                    <div class="stat-number">35</div>
                    <div class="stat-label">حقل بيانات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">مراحل</div>
                </div>
            </div>
        </div>

        <!-- Agricultural Fires -->
        <div class="intervention-type-card card-agricultural" onclick="openInterventionTable('agricultural')">
            <i class="fas fa-seedling card-icon"></i>
            <div class="card-title">📋 جدول حريق المحاصيل الزراعية</div>
            <div class="card-description">
                عرض تفاصيل حرائق المحاصيل الزراعية مع معلومات الخسائر والأملاك المنقذة والملاك المتضررين
            </div>
            <div class="card-stats">
                <div class="stat-item">
                    <div class="stat-number">42</div>
                    <div class="stat-label">حقل بيانات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">مراحل</div>
                </div>
            </div>
        </div>

        <!-- Building Fires -->
        <div class="intervention-type-card card-building" onclick="openInterventionTable('building')">
            <i class="fas fa-building card-icon"></i>
            <div class="card-title">📋 جدول حرائق البنايات والمؤسسات</div>
            <div class="card-description">
                عرض تفاصيل حرائق البنايات والمؤسسات مع معلومات الإجلاء والخسائر والأملاك المنقذة
            </div>
            <div class="card-stats">
                <div class="stat-item">
                    <div class="stat-number">38</div>
                    <div class="stat-label">حقل بيانات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">مراحل</div>
                </div>
            </div>
        </div>

        <!-- Miscellaneous Operations -->
        <div class="intervention-type-card card-misc" onclick="openInterventionTable('misc')">
            <i class="fas fa-tools card-icon"></i>
            <div class="card-title">📋 جدول العمليات المختلفة</div>
            <div class="card-description">
                عرض تفاصيل العمليات المختلفة والاستثنائية والجهاز الأمني مع التقارير الختامية
            </div>
            <div class="card-stats">
                <div class="stat-item">
                    <div class="stat-number">31</div>
                    <div class="stat-label">حقل بيانات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">مراحل</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🗂️ صفحة جميع التدخلات - تم التحميل');
    });
    
    // Open intervention table
    function openInterventionTable(type) {
        console.log('📋 فتح جدول التدخل:', type);
        
        const typeNames = {
            'medical': 'الإجلاء الصحي',
            'accident': 'حوادث المرور',
            'agricultural': 'حريق المحاصيل الزراعية',
            'building': 'حرائق البنايات والمؤسسات',
            'misc': 'العمليات المختلفة'
        };
        
        showNotification(`سيتم تطوير جدول ${typeNames[type]} قريباً`, 'info');
    }
    
    // Notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        `;
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
</script>
{% endblock %}
