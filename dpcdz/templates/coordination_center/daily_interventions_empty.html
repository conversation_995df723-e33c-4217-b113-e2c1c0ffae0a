{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التدخلات اليومية المتقدمة - الحماية المدنية الجزائرية</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">

<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .daily-interventions-container {
        padding: 20px;
        min-height: 100vh;
    }

    .page-header {
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        color: white;
        padding: 40px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 15px 35px rgba(238, 90, 36, 0.4);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        transform: rotate(45deg);
        animation: shine 3s infinite;
    }

    @keyframes shine {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .page-title {
        font-size: 3rem;
        font-weight: 700;
        margin: 0;
        text-align: center;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        text-align: center;
        margin-top: 15px;
        opacity: 0.95;
        font-size: 1.3rem;
        position: relative;
        z-index: 1;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
    }

    .main-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
        margin-bottom: 40px;
    }

    .action-card {
        background: linear-gradient(145deg, #ffffff, #f0f0f0);
        border-radius: 20px;
        padding: 35px 25px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: none;
        text-decoration: none;
        color: inherit;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .action-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        transition: left 0.5s;
    }

    .action-card:hover::before {
        left: 100%;
    }

    .action-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        text-decoration: none;
        color: inherit;
    }

    .action-icon {
        font-size: 4rem;
        margin-bottom: 20px;
        display: block;
        transition: all 0.3s ease;
    }

    .action-card:hover .action-icon {
        transform: scale(1.1) rotate(5deg);
    }

    .action-card.primary .action-icon {
        color: #4facfe;
        text-shadow: 0 0 20px rgba(79, 172, 254, 0.5);
    }

    .action-card.success .action-icon {
        color: #43e97b;
        text-shadow: 0 0 20px rgba(67, 233, 123, 0.5);
    }

    .action-card.warning .action-icon {
        color: #fa709a;
        text-shadow: 0 0 20px rgba(250, 112, 154, 0.5);
    }

    .action-card.info .action-icon {
        color: #38ef7d;
        text-shadow: 0 0 20px rgba(56, 239, 125, 0.5);
    }

    .action-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 15px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .action-description {
        color: #555;
        font-size: 1rem;
        line-height: 1.6;
        font-weight: 500;
    }

    .interventions-table-container {
        background: linear-gradient(145deg, #ffffff, #f8f9fa);
        border-radius: 25px;
        padding: 35px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        margin-bottom: 40px;
        border: 1px solid rgba(255,255,255,0.2);
    }

    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        padding-bottom: 20px;
        border-bottom: 3px solid #667eea;
        position: relative;
    }

    .table-header::after {
        content: '';
        position: absolute;
        bottom: -3px;
        left: 0;
        width: 100px;
        height: 3px;
        background: linear-gradient(90deg, #667eea, #764ba2);
        border-radius: 2px;
    }

    .table-title {
        font-size: 1.8rem;
        font-weight: 700;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin: 0;
    }

    .filter-controls {
        display: flex;
        gap: 15px;
        align-items: center;
    }

    .filter-select {
        padding: 12px 16px;
        border: 2px solid #e9ecef;
        border-radius: 15px;
        font-size: 1rem;
        background: white;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .filter-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }

    .interventions-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .interventions-table th,
    .interventions-table td {
        padding: 18px 15px;
        text-align: center;
        border-bottom: 1px solid #f1f3f4;
    }

    .interventions-table th {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        font-weight: 700;
        font-size: 1.1rem;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }

    .interventions-table tbody tr {
        transition: all 0.3s ease;
    }

    .interventions-table tbody tr:hover {
        background: linear-gradient(135deg, #f8f9ff, #fff8f8);
        transform: scale(1.01);
    }

    .status-badge {
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .status-initial {
        background: linear-gradient(135deg, #74b9ff, #0984e3);
        color: white;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
    }

    .status-reconnaissance {
        background: linear-gradient(135deg, #fdcb6e, #e17055);
        color: white;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
    }

    .status-intervention {
        background: linear-gradient(135deg, #55efc4, #00b894);
        color: white;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
    }

    .status-completed {
        background: linear-gradient(135deg, #fd79a8, #e84393);
        color: white;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
    }

    .action-btn {
        padding: 10px 20px;
        border: none;
        border-radius: 15px;
        font-size: 0.9rem;
        font-weight: 600;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }

    .btn-success {
        background: linear-gradient(135deg, #56ab2f, #a8e6cf);
        color: white;
    }

    .btn-info {
        background: linear-gradient(135deg, #29b6f6, #1e88e5);
        color: white;
    }

    .action-btn:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        text-decoration: none;
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 40px;
        color: #6c757d;
    }

    .empty-icon {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }

    /* Modal Styles */
    .modal-content {
        border-radius: 25px;
        border: none;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        overflow: hidden;
    }

    .modal-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-bottom: none;
        padding: 25px 30px;
        position: relative;
    }

    .modal-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: modalShine 2s infinite;
    }

    @keyframes modalShine {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .modal-title {
        font-weight: 700;
        font-size: 1.5rem;
        position: relative;
        z-index: 1;
    }

    .btn-close {
        filter: invert(1);
        position: relative;
        z-index: 1;
    }

    .modal-body {
        padding: 30px;
        background: linear-gradient(145deg, #ffffff, #f8f9fa);
    }

    .form-label {
        font-weight: 700;
        color: #2d3436;
        margin-bottom: 10px;
        font-size: 1rem;
    }

    .form-control, .form-select {
        border-radius: 15px;
        border: 2px solid #e9ecef;
        padding: 15px 20px;
        transition: all 0.3s ease;
        font-size: 1rem;
        background: white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: translateY(-2px);
    }

    .form-check-input {
        width: 1.2em;
        height: 1.2em;
        border-radius: 50%;
        border: 2px solid #667eea;
    }

    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
        box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
    }

    .form-check-label {
        cursor: pointer;
        font-weight: 500;
        margin-left: 10px;
    }

    .is-invalid {
        border-color: #e17055;
        box-shadow: 0 0 0 3px rgba(225, 112, 85, 0.1);
        animation: shake 0.5s;
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }

    .badge {
        font-size: 0.8rem;
        padding: 5px 10px;
        border-radius: 15px;
    }

    .modal-footer {
        border-top: none;
        padding: 20px 30px 30px;
        background: linear-gradient(145deg, #ffffff, #f8f9fa);
    }

    .btn {
        border-radius: 15px;
        padding: 12px 25px;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        font-size: 1rem;
    }

    .btn:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
    }

    .btn-secondary {
        background: linear-gradient(135deg, #95a5a6, #7f8c8d);
        border: none;
    }
</style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
<div class="daily-interventions-container">
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">🚨 التدخلات اليومية المتقدمة</h1>
        <p class="page-subtitle">مركز التنسيق العملي - إدارة التدخلات من البلاغ الأولي حتى إنهاء المهمة</p>
    </div>

    <!-- Main Action Buttons -->
    <div class="main-actions">
        <button class="action-card primary" onclick="openInitialReportModal()">
            <i class="fas fa-bullhorn action-icon"></i>
            <div class="action-title">📢 بلاغ أولي</div>
            <div class="action-description">تسجيل بلاغ تدخل جديد من أي جهة متصلة</div>
        </button>

        <button class="action-card success" onclick="openReconnaissanceModal()" disabled>
            <i class="fas fa-search action-icon"></i>
            <div class="action-title">🧭 عملية التعرف</div>
            <div class="action-description">تسجيل تفاصيل التدخل بعد وصول الفريق الميداني</div>
        </button>

        <button class="action-card warning" onclick="openTaskCompletionModal()" disabled>
            <i class="fas fa-check-circle action-icon"></i>
            <div class="action-title">✅ إنهاء المهمة</div>
            <div class="action-description">توثيق النتائج النهائية وإنهاء التدخل</div>
        </button>

        <a href="{% url 'all_interventions' %}" class="action-card info">
            <i class="fas fa-table action-icon"></i>
            <div class="action-title">🗂️ صفحة الجداول</div>
            <div class="action-description">عرض الجداول المتقدمة لجميع أنواع التدخلات</div>
        </a>
    </div>

    <!-- Interventions Table -->
    <div class="interventions-table-container">
        <div class="table-header">
            <h2 class="table-title">📋 جدول التدخلات اليومية</h2>
            <div class="filter-controls">
                <select class="filter-select" id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="initial_report">بلاغ أولي</option>
                    <option value="reconnaissance">قيد التعرف</option>
                    <option value="intervention">عملية تدخل</option>
                    <option value="completed">منتهية</option>
                    <option value="escalated">كارثة كبرى</option>
                </select>
                <select class="filter-select" id="typeFilter">
                    <option value="">جميع الأنواع</option>
                    <option value="medical">إجلاء صحي</option>
                    <option value="accident">حادث مرور</option>
                    <option value="agricultural-fire">حريق محاصيل زراعية</option>
                    <option value="building-fire">حرائق البنايات والمؤسسات</option>
                    <option value="other">عمليات مختلفة</option>
                </select>
            </div>
        </div>

        <table class="interventions-table" id="interventionsTable">
            <thead>
                <tr>
                    <th>رقم</th>
                    <th>وقت التدخل</th>
                    <th>نوع التدخل</th>
                    <th>مكان التدخل</th>
                    <th>الوحدة</th>
                    <th>الوسيلة</th>
                    <th>الحالة</th>
                    <th>إجراء</th>
                </tr>
            </thead>
            <tbody>
                <!-- Sample data - will be replaced with dynamic content -->
                <tr>
                    <td>001</td>
                    <td>08:30</td>
                    <td>حريق محاصيل زراعية</td>
                    <td>بئر بوحوش</td>
                    <td>وحدة سوق أهراس</td>
                    <td>FPT-05</td>
                    <td><span class="status-badge status-reconnaissance">قيد التعرف</span></td>
                    <td>
                        <button class="action-btn btn-primary" onclick="openReconnaissanceModal(1)">
                            📝 عملية التعرف
                        </button>
                    </td>
                </tr>
                <tr>
                    <td>002</td>
                    <td>10:15</td>
                    <td>إجلاء صحي</td>
                    <td>المشروحة</td>
                    <td>وحدة سوق أهراس</td>
                    <td>AMB-02</td>
                    <td><span class="status-badge status-intervention">عملية تدخل</span></td>
                    <td>
                        <button class="action-btn btn-success" onclick="openTaskCompletionModal(2)">
                            ✅ إنهاء المهمة
                        </button>
                    </td>
                </tr>
                <tr>
                    <td>003</td>
                    <td>14:45</td>
                    <td>حادث مرور</td>
                    <td>الطريق الوطني رقم 16</td>
                    <td>وحدة سوق أهراس</td>
                    <td>FPT-01, AMB-01</td>
                    <td><span class="status-badge status-completed">منتهية</span></td>
                    <td>
                        <button class="action-btn btn-info" onclick="viewInterventionDetails(3)">
                            👁️ عرض التفاصيل
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- Empty State -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <div class="empty-icon">
                <i class="fas fa-clipboard-list"></i>
            </div>
            <h3>لا توجد تدخلات مسجلة</h3>
            <p>ابدأ بتسجيل بلاغ أولي لإنشاء تدخل جديد</p>
        </div>
    </div>
</div>

<!-- Initial Report Modal -->
<div class="modal fade" id="initialReportModal" tabindex="-1" aria-labelledby="initialReportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="initialReportModalLabel">📢 بلاغ أولي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="initialReportForm">
                    <div class="row">
                        <!-- Exit Time -->
                        <div class="col-md-6 mb-3">
                            <label for="exitTime" class="form-label">ساعة ودقيقة الخروج *</label>
                            <input type="time" class="form-control" id="exitTime" name="exit_time" required>
                        </div>

                        <!-- Intervention Location -->
                        <div class="col-md-6 mb-3">
                            <label for="interventionLocation" class="form-label">مكان التدخل *</label>
                            <input type="text" class="form-control" id="interventionLocation" name="intervention_location" required placeholder="أدخل مكان التدخل">
                        </div>
                    </div>

                    <div class="row">
                        <!-- Intervention Type -->
                        <div class="col-md-6 mb-3">
                            <label for="interventionType" class="form-label">نوع التدخل *</label>
                            <select class="form-select" id="interventionType" name="intervention_type" required>
                                <option value="">اختر نوع التدخل</option>
                                <option value="medical">إجلاء صحي</option>
                                <option value="accident">حادث مرور</option>
                                <option value="agricultural-fire">حريق محاصيل زراعية</option>
                                <option value="building-fire">حرائق البنايات والمؤسسات</option>
                                <option value="other">عمليات مختلفة</option>
                            </select>
                        </div>

                        <!-- Contact Source -->
                        <div class="col-md-6 mb-3">
                            <label for="contactSource" class="form-label">الجهة المتصلة *</label>
                            <select class="form-select" id="contactSource" name="contact_source" required>
                                <option value="">اختر الجهة المتصلة</option>
                                <option value="citizen">مواطن</option>
                                <option value="police">الشرطة</option>
                                <option value="gendarmerie">الدرك الوطني</option>
                                <option value="army">الجيش الوطني الشعبي</option>
                                <option value="forest">مصالح الغابات</option>
                                <option value="customs">الجمارك</option>
                                <option value="local-authorities">السلطات المحلية</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Contact Type -->
                        <div class="col-md-6 mb-3">
                            <label for="contactType" class="form-label">نوع الاتصال *</label>
                            <select class="form-select" id="contactType" name="contact_type" required>
                                <option value="">اختر نوع الاتصال</option>
                                <option value="phone">هاتف</option>
                                <option value="radio">راديو</option>
                                <option value="unit-request">وحدة تطلب الدعم</option>
                                <option value="direct">مباشر</option>
                            </select>
                        </div>

                        <!-- Phone Number -->
                        <div class="col-md-6 mb-3">
                            <label for="phoneNumber" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phoneNumber" name="phone_number" placeholder="أدخل رقم الهاتف">
                        </div>
                    </div>

                    <!-- Assigned Vehicles -->
                    <div class="mb-3">
                        <label for="assignedVehicles" class="form-label">الوسيلة المرسلة *</label>
                        <div id="vehiclesList" class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                            <div class="text-muted">جاري تحميل الوسائل المتاحة...</div>
                        </div>
                        <small class="form-text text-muted">يمكن اختيار أكثر من وسيلة</small>
                    </div>

                    <!-- Additional Notes -->
                    <div class="mb-3">
                        <label for="additionalNotes" class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" id="additionalNotes" name="additional_notes" rows="3" placeholder="أدخل أي ملاحظات إضافية"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="submitInitialReport()">
                    <i class="fas fa-save"></i> حفظ البلاغ
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Other modals will be added here -->
<div id="modalContainer"></div>
    </main>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Sidebar JS -->
    <script src="{% static 'js/sidebar.js' %}"></script>

<script>
    // Global variables
    let currentInterventions = [];

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚨 صفحة التدخلات اليومية المتقدمة - تم التحميل');
        loadInterventions();
        setupFilters();
    });

    // Load interventions from server
    function loadInterventions() {
        // This will be connected to Django backend later
        console.log('📊 تحميل التدخلات...');
        // For now, using sample data
        updateInterventionsTable();
    }

    // Setup filter functionality
    function setupFilters() {
        const statusFilter = document.getElementById('statusFilter');
        const typeFilter = document.getElementById('typeFilter');

        statusFilter.addEventListener('change', filterInterventions);
        typeFilter.addEventListener('change', filterInterventions);
    }

    // Filter interventions based on selected criteria
    function filterInterventions() {
        const statusFilter = document.getElementById('statusFilter').value;
        const typeFilter = document.getElementById('typeFilter').value;
        const table = document.getElementById('interventionsTable');
        const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

        let visibleRows = 0;

        for (let row of rows) {
            let showRow = true;

            // Status filter
            if (statusFilter && !row.innerHTML.includes(getStatusText(statusFilter))) {
                showRow = false;
            }

            // Type filter
            if (typeFilter && !row.innerHTML.includes(getTypeText(typeFilter))) {
                showRow = false;
            }

            row.style.display = showRow ? '' : 'none';
            if (showRow) visibleRows++;
        }

        // Show/hide empty state
        const emptyState = document.getElementById('emptyState');
        emptyState.style.display = visibleRows === 0 ? 'block' : 'none';
        table.style.display = visibleRows === 0 ? 'none' : 'table';
    }

    // Helper functions for filter text mapping
    function getStatusText(status) {
        const statusMap = {
            'initial_report': 'بلاغ أولي',
            'reconnaissance': 'قيد التعرف',
            'intervention': 'عملية تدخل',
            'completed': 'منتهية',
            'escalated': 'كارثة كبرى'
        };
        return statusMap[status] || '';
    }

    function getTypeText(type) {
        const typeMap = {
            'medical': 'إجلاء صحي',
            'accident': 'حادث مرور',
            'agricultural-fire': 'حريق محاصيل زراعية',
            'building-fire': 'حرائق البنايات والمؤسسات',
            'other': 'عمليات مختلفة'
        };
        return typeMap[type] || '';
    }

    // Modal functions
    function openInitialReportModal() {
        console.log('📢 فتح نموذج البلاغ الأولي');

        // Set current time as default
        const now = new Date();
        const timeString = now.toTimeString().slice(0, 5);
        document.getElementById('exitTime').value = timeString;

        // Load available vehicles
        loadAvailableVehicles();

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('initialReportModal'));
        modal.show();
    }

    // Load available vehicles from server
    function loadAvailableVehicles() {
        const vehiclesList = document.getElementById('vehiclesList');
        vehiclesList.innerHTML = '<div class="text-muted">جاري تحميل الوسائل المتاحة...</div>';

        fetch('/api/get-available-vehicles/', {
            method: 'GET',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.vehicles) {
                let vehiclesHtml = '';
                data.vehicles.forEach(vehicle => {
                    const isDisabled = !vehicle.available ? 'disabled' : '';
                    const statusClass = vehicle.available ? 'text-success' : 'text-danger';

                    vehiclesHtml += `
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" value="${vehicle.id}"
                                   id="vehicle_${vehicle.id}" name="assigned_vehicles" ${isDisabled}>
                            <label class="form-check-label" for="vehicle_${vehicle.id}">
                                <strong>${vehicle.name}</strong> - ${vehicle.type}
                                <span class="badge ${vehicle.available ? 'bg-success' : 'bg-danger'} ms-2">
                                    ${vehicle.status}
                                </span>
                            </label>
                        </div>
                    `;
                });

                if (vehiclesHtml === '') {
                    vehiclesHtml = '<div class="text-muted">لا توجد وسائل متاحة</div>';
                }

                vehiclesList.innerHTML = vehiclesHtml;
            } else {
                vehiclesList.innerHTML = '<div class="text-danger">خطأ في تحميل الوسائل</div>';
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل الوسائل:', error);
            vehiclesList.innerHTML = '<div class="text-danger">خطأ في الاتصال بالخادم</div>';
        });
    }

    // Submit initial report
    function submitInitialReport() {
        const form = document.getElementById('initialReportForm');
        const formData = new FormData(form);

        // Get selected vehicles
        const selectedVehicles = [];
        const vehicleCheckboxes = document.querySelectorAll('input[name="assigned_vehicles"]:checked');
        vehicleCheckboxes.forEach(checkbox => {
            selectedVehicles.push(checkbox.value);
        });

        if (selectedVehicles.length === 0) {
            showNotification('يجب اختيار وسيلة واحدة على الأقل', 'warning');
            return;
        }

        // Validate required fields
        const requiredFields = ['exit_time', 'intervention_location', 'intervention_type', 'contact_source', 'contact_type'];
        let isValid = true;

        requiredFields.forEach(field => {
            const input = document.getElementById(field.replace('_', ''));
            if (!input || !input.value.trim()) {
                isValid = false;
                if (input) {
                    input.classList.add('is-invalid');
                }
            } else {
                if (input) {
                    input.classList.remove('is-invalid');
                }
            }
        });

        if (!isValid) {
            showNotification('يرجى ملء جميع الحقول المطلوبة', 'danger');
            return;
        }

        // Prepare data for submission
        const reportData = {
            exit_time: formData.get('exit_time'),
            intervention_location: formData.get('intervention_location'),
            intervention_type: formData.get('intervention_type'),
            contact_source: formData.get('contact_source'),
            contact_type: formData.get('contact_type'),
            phone_number: formData.get('phone_number'),
            additional_notes: formData.get('additional_notes'),
            assigned_vehicles: selectedVehicles
        };

        console.log('📊 بيانات البلاغ الأولي:', reportData);

        // Submit to Django backend
        fetch('/api/create-initial-report/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify(reportData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`تم حفظ البلاغ الأولي بنجاح! رقم التدخل: ${data.intervention_number}`, 'success');

                // Close modal and refresh table
                const modal = bootstrap.Modal.getInstance(document.getElementById('initialReportModal'));
                modal.hide();

                // Reset form
                form.reset();

                // Refresh interventions table
                setTimeout(() => {
                    updateInterventionsTable();
                }, 1000);
            } else {
                showNotification(data.error || 'حدث خطأ في حفظ البلاغ', 'danger');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showNotification('حدث خطأ في الاتصال بالخادم', 'danger');
        });

    }

    function openReconnaissanceModal(interventionId = null) {
        console.log('🧭 فتح نموذج عملية التعرف', interventionId);
        showNotification('سيتم تطوير نموذج عملية التعرف قريباً', 'info');
    }

    function openTaskCompletionModal(interventionId = null) {
        console.log('✅ فتح نموذج إنهاء المهمة', interventionId);
        showNotification('سيتم تطوير نموذج إنهاء المهمة قريباً', 'info');
    }

    function viewInterventionDetails(interventionId) {
        console.log('👁️ عرض تفاصيل التدخل', interventionId);
        showNotification('سيتم تطوير صفحة تفاصيل التدخل قريباً', 'info');
    }

    // Update interventions table
    function updateInterventionsTable() {
        console.log('🔄 تحديث جدول التدخلات');
        // This function will be expanded when connected to backend
    }

    // Notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        `;
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    // Get CSRF token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Auto-refresh functionality
    setInterval(function() {
        loadInterventions();
    }, 30000); // Refresh every 30 seconds
</script>

</body>
</html>
