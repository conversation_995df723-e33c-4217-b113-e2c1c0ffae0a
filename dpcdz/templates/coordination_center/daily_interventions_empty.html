{% extends 'base.html' %}
{% load static %}

{% block title %}التدخلات اليومية المتقدمة{% endblock %}

{% block extra_css %}
<style>
    .daily-interventions-container {
        padding: 20px;
        background: #f8f9fa;
        min-height: 100vh;
    }

    .page-header {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 300;
        margin: 0;
        text-align: center;
    }

    .page-subtitle {
        text-align: center;
        margin-top: 10px;
        opacity: 0.9;
        font-size: 1.1rem;
    }

    .main-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .action-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border: none;
        text-decoration: none;
        color: inherit;
        cursor: pointer;
    }

    .action-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        text-decoration: none;
        color: inherit;
    }

    .action-icon {
        font-size: 3rem;
        margin-bottom: 15px;
        display: block;
    }

    .action-card.primary .action-icon {
        color: #007bff;
    }

    .action-card.success .action-icon {
        color: #28a745;
    }

    .action-card.warning .action-icon {
        color: #ffc107;
    }

    .action-card.info .action-icon {
        color: #17a2b8;
    }

    .action-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .action-description {
        color: #6c757d;
        font-size: 0.95rem;
        line-height: 1.5;
    }

    .interventions-table-container {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }

    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid #e9ecef;
    }

    .table-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #495057;
        margin: 0;
    }

    .filter-controls {
        display: flex;
        gap: 10px;
        align-items: center;
    }

    .filter-select {
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 8px;
        font-size: 0.9rem;
    }

    .interventions-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 15px;
    }

    .interventions-table th,
    .interventions-table td {
        padding: 12px;
        text-align: center;
        border-bottom: 1px solid #e9ecef;
    }

    .interventions-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #495057;
        border-top: 2px solid #007bff;
    }

    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }

    .status-initial {
        background: #e3f2fd;
        color: #1976d2;
    }

    .status-reconnaissance {
        background: #fff3e0;
        color: #f57c00;
    }

    .status-intervention {
        background: #e8f5e8;
        color: #388e3c;
    }

    .status-completed {
        background: #f3e5f5;
        color: #7b1fa2;
    }

    .action-btn {
        padding: 6px 12px;
        border: none;
        border-radius: 6px;
        font-size: 0.85rem;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        transition: all 0.2s ease;
    }

    .btn-primary {
        background: #007bff;
        color: white;
    }

    .btn-success {
        background: #28a745;
        color: white;
    }

    .btn-info {
        background: #17a2b8;
        color: white;
    }

    .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        text-decoration: none;
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 40px;
        color: #6c757d;
    }

    .empty-icon {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="daily-interventions-container">
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">🚨 التدخلات اليومية المتقدمة</h1>
        <p class="page-subtitle">مركز التنسيق العملي - إدارة التدخلات من البلاغ الأولي حتى إنهاء المهمة</p>
    </div>

    <!-- Main Action Buttons -->
    <div class="main-actions">
        <button class="action-card primary" onclick="openInitialReportModal()">
            <i class="fas fa-bullhorn action-icon"></i>
            <div class="action-title">📢 بلاغ أولي</div>
            <div class="action-description">تسجيل بلاغ تدخل جديد من أي جهة متصلة</div>
        </button>

        <button class="action-card success" onclick="openReconnaissanceModal()" disabled>
            <i class="fas fa-search action-icon"></i>
            <div class="action-title">🧭 عملية التعرف</div>
            <div class="action-description">تسجيل تفاصيل التدخل بعد وصول الفريق الميداني</div>
        </button>

        <button class="action-card warning" onclick="openTaskCompletionModal()" disabled>
            <i class="fas fa-check-circle action-icon"></i>
            <div class="action-title">✅ إنهاء المهمة</div>
            <div class="action-description">توثيق النتائج النهائية وإنهاء التدخل</div>
        </button>

        <a href="{% url 'all_interventions' %}" class="action-card info">
            <i class="fas fa-table action-icon"></i>
            <div class="action-title">🗂️ صفحة الجداول</div>
            <div class="action-description">عرض الجداول المتقدمة لجميع أنواع التدخلات</div>
        </a>
    </div>

    <!-- Interventions Table -->
    <div class="interventions-table-container">
        <div class="table-header">
            <h2 class="table-title">📋 جدول التدخلات اليومية</h2>
            <div class="filter-controls">
                <select class="filter-select" id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="initial_report">بلاغ أولي</option>
                    <option value="reconnaissance">قيد التعرف</option>
                    <option value="intervention">عملية تدخل</option>
                    <option value="completed">منتهية</option>
                    <option value="escalated">كارثة كبرى</option>
                </select>
                <select class="filter-select" id="typeFilter">
                    <option value="">جميع الأنواع</option>
                    <option value="medical">إجلاء صحي</option>
                    <option value="accident">حادث مرور</option>
                    <option value="agricultural-fire">حريق محاصيل زراعية</option>
                    <option value="building-fire">حرائق البنايات والمؤسسات</option>
                    <option value="other">عمليات مختلفة</option>
                </select>
            </div>
        </div>

        <table class="interventions-table" id="interventionsTable">
            <thead>
                <tr>
                    <th>رقم</th>
                    <th>وقت التدخل</th>
                    <th>نوع التدخل</th>
                    <th>مكان التدخل</th>
                    <th>الوحدة</th>
                    <th>الوسيلة</th>
                    <th>الحالة</th>
                    <th>إجراء</th>
                </tr>
            </thead>
            <tbody>
                <!-- Sample data - will be replaced with dynamic content -->
                <tr>
                    <td>001</td>
                    <td>08:30</td>
                    <td>حريق محاصيل زراعية</td>
                    <td>بئر بوحوش</td>
                    <td>وحدة سوق أهراس</td>
                    <td>FPT-05</td>
                    <td><span class="status-badge status-reconnaissance">قيد التعرف</span></td>
                    <td>
                        <button class="action-btn btn-primary" onclick="openReconnaissanceModal(1)">
                            📝 عملية التعرف
                        </button>
                    </td>
                </tr>
                <tr>
                    <td>002</td>
                    <td>10:15</td>
                    <td>إجلاء صحي</td>
                    <td>المشروحة</td>
                    <td>وحدة سوق أهراس</td>
                    <td>AMB-02</td>
                    <td><span class="status-badge status-intervention">عملية تدخل</span></td>
                    <td>
                        <button class="action-btn btn-success" onclick="openTaskCompletionModal(2)">
                            ✅ إنهاء المهمة
                        </button>
                    </td>
                </tr>
                <tr>
                    <td>003</td>
                    <td>14:45</td>
                    <td>حادث مرور</td>
                    <td>الطريق الوطني رقم 16</td>
                    <td>وحدة سوق أهراس</td>
                    <td>FPT-01, AMB-01</td>
                    <td><span class="status-badge status-completed">منتهية</span></td>
                    <td>
                        <button class="action-btn btn-info" onclick="viewInterventionDetails(3)">
                            👁️ عرض التفاصيل
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- Empty State -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <div class="empty-icon">
                <i class="fas fa-clipboard-list"></i>
            </div>
            <h3>لا توجد تدخلات مسجلة</h3>
            <p>ابدأ بتسجيل بلاغ أولي لإنشاء تدخل جديد</p>
        </div>
    </div>
</div>

<!-- Modals will be added here -->
<div id="modalContainer"></div>

{% endblock %}

{% block extra_js %}
<script>
    // Global variables
    let currentInterventions = [];

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚨 صفحة التدخلات اليومية المتقدمة - تم التحميل');
        loadInterventions();
        setupFilters();
    });

    // Load interventions from server
    function loadInterventions() {
        // This will be connected to Django backend later
        console.log('📊 تحميل التدخلات...');
        // For now, using sample data
        updateInterventionsTable();
    }

    // Setup filter functionality
    function setupFilters() {
        const statusFilter = document.getElementById('statusFilter');
        const typeFilter = document.getElementById('typeFilter');

        statusFilter.addEventListener('change', filterInterventions);
        typeFilter.addEventListener('change', filterInterventions);
    }

    // Filter interventions based on selected criteria
    function filterInterventions() {
        const statusFilter = document.getElementById('statusFilter').value;
        const typeFilter = document.getElementById('typeFilter').value;
        const table = document.getElementById('interventionsTable');
        const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

        let visibleRows = 0;

        for (let row of rows) {
            let showRow = true;

            // Status filter
            if (statusFilter && !row.innerHTML.includes(getStatusText(statusFilter))) {
                showRow = false;
            }

            // Type filter
            if (typeFilter && !row.innerHTML.includes(getTypeText(typeFilter))) {
                showRow = false;
            }

            row.style.display = showRow ? '' : 'none';
            if (showRow) visibleRows++;
        }

        // Show/hide empty state
        const emptyState = document.getElementById('emptyState');
        emptyState.style.display = visibleRows === 0 ? 'block' : 'none';
        table.style.display = visibleRows === 0 ? 'none' : 'table';
    }

    // Helper functions for filter text mapping
    function getStatusText(status) {
        const statusMap = {
            'initial_report': 'بلاغ أولي',
            'reconnaissance': 'قيد التعرف',
            'intervention': 'عملية تدخل',
            'completed': 'منتهية',
            'escalated': 'كارثة كبرى'
        };
        return statusMap[status] || '';
    }

    function getTypeText(type) {
        const typeMap = {
            'medical': 'إجلاء صحي',
            'accident': 'حادث مرور',
            'agricultural-fire': 'حريق محاصيل زراعية',
            'building-fire': 'حرائق البنايات والمؤسسات',
            'other': 'عمليات مختلفة'
        };
        return typeMap[type] || '';
    }

    // Modal functions
    function openInitialReportModal() {
        console.log('📢 فتح نموذج البلاغ الأولي');
        showNotification('سيتم تطوير نموذج البلاغ الأولي قريباً', 'info');
    }

    function openReconnaissanceModal(interventionId = null) {
        console.log('🧭 فتح نموذج عملية التعرف', interventionId);
        showNotification('سيتم تطوير نموذج عملية التعرف قريباً', 'info');
    }

    function openTaskCompletionModal(interventionId = null) {
        console.log('✅ فتح نموذج إنهاء المهمة', interventionId);
        showNotification('سيتم تطوير نموذج إنهاء المهمة قريباً', 'info');
    }

    function viewInterventionDetails(interventionId) {
        console.log('👁️ عرض تفاصيل التدخل', interventionId);
        showNotification('سيتم تطوير صفحة تفاصيل التدخل قريباً', 'info');
    }

    // Update interventions table
    function updateInterventionsTable() {
        console.log('🔄 تحديث جدول التدخلات');
        // This function will be expanded when connected to backend
    }

    // Notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        `;
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    // Auto-refresh functionality
    setInterval(function() {
        loadInterventions();
    }, 30000); // Refresh every 30 seconds
</script>
{% endblock %}
